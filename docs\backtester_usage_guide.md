# Backtester 使用说明文档

## 功能概述

`src/backtester.py` 是一个功能强大的股票交易策略回测系统，主要用于：

- **历史数据回测**：基于历史数据模拟交易策略的表现
- **多源新闻分析**：集成Alpha Vantage、Finnhub、NewsAPI三个数据源的新闻数据
- **AI驱动决策**：使用大语言模型（LLM）进行智能交易决策
- **多空交易支持**：支持做多和做空操作，包括保证金管理
- **实时准确性跟踪**：跟踪分析师信号的准确性
- **本地数据支持**：支持使用本地新闻数据，避免API速率限制

## 核心类和方法说明

### Backtester 类

#### 初始化参数

```python
Backtester(
    agent: Callable,                    # 交易代理函数
    tickers: list[str],                 # 股票代码列表
    start_date: str,                    # 开始日期 (YYYY-MM-DD)
    end_date: str,                      # 结束日期 (YYYY-MM-DD)
    initial_capital: float,             # 初始资金
    model_name: str = "gpt-4o",         # LLM模型名称
    model_provider: str = "OpenAI",     # LLM提供商
    selected_analysts: list[str] = [],  # 选择的分析师
    initial_margin_requirement: float = 0.0,  # 保证金要求
    show_reasoning: bool = False,       # 是否显示推理过程
    save_reasoning: bool = False,       # 是否保存推理日志
    save_input_data: bool = False,      # 是否保存输入数据
    track_accuracy: bool = False,       # 是否跟踪准确性
)
```

#### 主要方法

##### 1. `run_backtest()` - 执行回测

**功能**：执行完整的回测流程

**返回值**：`Dict` - 包含性能指标的字典

**示例**：
```python
backtester = Backtester(
    agent=run_hedge_fund,
    tickers=["AAPL"],
    start_date="2024-01-01",
    end_date="2024-01-31",
    initial_capital=100000
)

performance_metrics = backtester.run_backtest()
```

##### 2. `execute_trade()` - 执行交易

**功能**：执行买入、卖出、做空、平仓操作

**参数**：
- `ticker: str` - 股票代码
- `action: str` - 交易动作 ("buy", "sell", "short", "cover")
- `quantity: float` - 交易数量
- `current_price: float` - 当前价格

**返回值**：`int` - 实际执行的股票数量

##### 3. `calculate_portfolio_value()` - 计算投资组合价值

**功能**：计算当前投资组合的总价值

**参数**：
- `current_prices: Dict[str, float]` - 当前股价字典

**返回值**：`float` - 投资组合总价值

##### 4. `analyze_performance()` - 分析性能

**功能**：生成性能分析报告和图表

**返回值**：`pd.DataFrame` - 包含性能数据的DataFrame

## 使用流程

### 1. 基本回测流程

```python
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from src.backtester import Backtester
from src.main import run_hedge_fund

# 创建回测器
backtester = Backtester(
    agent=run_hedge_fund,
    tickers=["AAPL", "MSFT"],
    start_date="2024-01-01",
    end_date="2024-01-31",
    initial_capital=100000,
    model_name="gpt-4o",
    selected_analysts=["technical_analyst", "fundamental_analyst"],
    show_reasoning=True
)

# 执行回测
performance_metrics = backtester.run_backtest()

# 分析结果
performance_df = backtester.analyze_performance()
```

### 2. 启用本地新闻数据模式

```python
# 方法1：通过环境变量
import os
os.environ["NEWS_USE_LOCAL_DATA"] = "true"
os.environ["NEWS_SOURCES"] = "alpha_vantage,newsapi,finnhub"
os.environ["NEWS_TIME_OFFSET_DAYS"] = "1"

# 方法2：通过配置文件
from src.config.news_config import news_config

news_config.set_use_local_data(True)
news_config.set_selected_sources(["alpha_vantage", "newsapi", "finnhub"])
news_config.set_time_offset_days(1)
news_config.save_config()

# 执行回测
backtester = Backtester(
    agent=run_hedge_fund,
    tickers=["AAPL"],
    start_date="2024-01-01",
    end_date="2024-01-31",
    initial_capital=100000
)

performance_metrics = backtester.run_backtest()
```

### 3. 命令行使用

```bash
# 基本回测
python src/backtester.py \
    --tickers AAPL,MSFT \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --initial-capital 100000 \
    --show-reasoning

# 使用本地新闻数据
python src/backtester.py \
    --tickers AAPL \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --initial-capital 100000 \
    --use-local-news \
    --news-sources alpha_vantage,newsapi,finnhub \
    --news-time-offset 1 \
    --save-reasoning \
    --track-accuracy

# 启用确定性模式（可重现结果）
python src/backtester.py \
    --tickers AAPL \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --initial-capital 100000 \
    --deterministic \
    --seed 42 \
    --experiment-id my_experiment
```
 python src/backtester.py --tickers AAPL --track-accuracy --save-reasoning --save-input-data --start-date 2025-05-15 --end-date 2025-06-15 --use-local-news --news-sources alpha_vantage --news-time-offset 1

## 配置说明

### 环境变量配置

```bash
# LLM API密钥
export OPENAI_API_KEY="your_openai_key"
export ANTHROPIC_API_KEY="your_anthropic_key"

# 金融数据API密钥
export FINANCIAL_DATASETS_API_KEY="your_financial_datasets_key"
export ALPHA_VANTAGE_API_KEY="your_alpha_vantage_key"
export NEWSAPI_KEY="your_newsapi_key"
export FINNHUB_API_KEY="your_finnhub_key"

# 新闻数据配置
export NEWS_USE_LOCAL_DATA="true"
export NEWS_SOURCES="alpha_vantage,newsapi,finnhub"
export NEWS_TIME_OFFSET_DAYS="1"
```

### 本地新闻数据目录结构

```
项目根目录/
├── AAPL_alpha_news/
│   ├── alpha_news_2024-01-01.json
│   ├── alpha_news_2024-01-02.json
│   └── ...
├── AAPL_news_api_news/
│   ├── news_api_2024-01-01.json
│   ├── news_api_2024-01-02.json
│   └── ...
└── AAPL_finnhub_news/
    ├── finnhub_2024-01-01.json
    ├── finnhub_2024-01-02.json
    └── ...
```

### 新闻配置文件 (news_config.json)

```json
{
  "default_settings": {
    "use_local_data": true,
    "time_offset_days": 1,
    "max_articles_per_source": 10,
    "fallback_to_api": false,
    "selected_sources": ["alpha_vantage", "newsapi", "finnhub"]
  },
  "local_data_directories": {
    "alpha_vantage": "AAPL_alpha_news",
    "newsapi": "AAPL_news_api_news",
    "finnhub": "AAPL_finnhub_news"
  }
}
```

## 输出结果说明

### 回测过程输出

回测过程中会显示：
- 数据预取进度
- 每日交易决策
- 分析师信号统计
- 投资组合价值变化
- 性能指标更新

### 性能指标

```python
performance_metrics = {
    "sharpe_ratio": 1.25,           # 夏普比率
    "sortino_ratio": 1.45,          # 索提诺比率
    "max_drawdown": -5.2,           # 最大回撤 (%)
    "max_drawdown_date": "2024-01-15"  # 最大回撤日期
}
```

### 分析报告

`analyze_performance()` 方法会生成：
- 总收益率
- 已实现盈亏
- 夏普比率
- 最大回撤
- 胜率
- 盈亏比
- 最大连续盈利/亏损天数
- 投资组合价值曲线图

## 本地新闻数据支持

### 当前支持状态

✅ **已完全支持本地新闻数据功能**

系统已经实现了完整的本地新闻数据支持，包括：

1. **配置管理** (`src/config/news_config.py`)
   - 支持三个数据源：Alpha Vantage、NewsAPI、Finnhub
   - 灵活的配置选项和环境变量支持
   - 自动检测本地数据可用性

2. **数据读取** (`src/tools/local_news_reader.py`)
   - 统一的数据格式转换
   - 智能的日期匹配和时间偏移
   - 错误处理和降级机制

3. **API集成** (`src/tools/api.py`)
   - `get_formatted_multi_source_news()` 函数自动检测并使用本地数据
   - 无缝的API/本地数据切换
   - 保持与现有代码的兼容性

### 使用本地新闻数据的优势

1. **避免API速率限制**：不受第三方API调用限制
2. **提高回测速度**：本地读取比网络请求快
3. **确保数据一致性**：使用固定的历史数据集
4. **降低成本**：减少API调用费用
5. **离线工作**：无需网络连接即可运行回测

### 配置本地新闻数据

```python
from src.config.news_config import news_config, print_news_config_status

# 检查当前配置状态
print_news_config_status()

# 启用本地数据模式
news_config.set_use_local_data(True)
news_config.set_selected_sources(["alpha_vantage", "newsapi", "finnhub"])
news_config.set_time_offset_days(1)

# 保存配置
news_config.save_config()
```

## 注意事项

1. **数据准备**：确保本地新闻数据文件按照指定格式和命名规则存放
2. **时间偏移**：建议设置1天的时间偏移，避免使用未来数据
3. **内存使用**：大量历史数据可能占用较多内存
4. **API密钥**：即使使用本地数据，某些功能仍需要API密钥
5. **确定性模式**：使用 `--deterministic` 参数确保结果可重现

## 故障排除

### 常见问题

1. **本地数据不可用**
   ```python
   from src.config.news_config import news_config
   availability = news_config.check_local_data_availability()
   print(availability)
   ```

2. **API密钥错误**
   ```bash
   # 检查环境变量
   echo $OPENAI_API_KEY
   echo $FINANCIAL_DATASETS_API_KEY
   ```

3. **内存不足**
   - 减少回测时间范围
   - 降低新闻数量限制
   - 使用更少的股票代码

4. **性能问题**
   - 启用本地数据模式
   - 使用更快的LLM模型
   - 减少分析师数量

## 高级用法示例

### 1. 自定义交易策略回测

```python
from src.backtester import Backtester
from src.main import run_hedge_fund
from datetime import datetime, timedelta

def custom_backtest_with_analysis():
    """自定义回测示例，包含详细分析"""

    # 设置回测参数
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")

    # 创建回测器
    backtester = Backtester(
        agent=run_hedge_fund,
        tickers=["AAPL"],
        start_date=start_date,
        end_date=end_date,
        initial_capital=100000,
        model_name="gpt-4o",
        model_provider="OpenAI",
        selected_analysts=["technical_analyst", "fundamental_analyst", "news_analyst"],
        initial_margin_requirement=0.5,  # 50% 保证金要求
        show_reasoning=True,
        save_reasoning=True,
        save_input_data=True,
        track_accuracy=True
    )

    # 执行回测
    print("🚀 开始执行回测...")
    performance_metrics = backtester.run_backtest()

    # 分析结果
    print("\n📊 分析回测结果...")
    performance_df = backtester.analyze_performance()

    # 打印关键指标
    print(f"\n📈 关键性能指标:")
    print(f"夏普比率: {performance_metrics.get('sharpe_ratio', 'N/A'):.2f}")
    print(f"最大回撤: {performance_metrics.get('max_drawdown', 'N/A'):.2f}%")

    return performance_df, performance_metrics

# 运行示例
if __name__ == "__main__":
    performance_df, metrics = custom_backtest_with_analysis()
```

### 2. 多股票组合回测

```python
def multi_stock_portfolio_backtest():
    """多股票投资组合回测示例"""

    # 科技股组合
    tech_stocks = ["AAPL", "MSFT", "GOOGL", "AMZN"]

    backtester = Backtester(
        agent=run_hedge_fund,
        tickers=tech_stocks,
        start_date="2024-01-01",
        end_date="2024-02-01",
        initial_capital=500000,  # 更大的初始资金
        model_name="gpt-4o",
        selected_analysts=["technical_analyst", "fundamental_analyst"],
        show_reasoning=False,  # 关闭推理显示以提高速度
        track_accuracy=True
    )

    # 执行回测
    performance_metrics = backtester.run_backtest()
    performance_df = backtester.analyze_performance()

    # 计算每只股票的贡献
    final_positions = backtester.portfolio["positions"]
    print("\n📊 最终持仓分析:")
    for ticker in tech_stocks:
        pos = final_positions[ticker]
        net_shares = pos["long"] - pos["short"]
        print(f"{ticker}: {net_shares} 股 (多头: {pos['long']}, 空头: {pos['short']})")

    return performance_df

# 运行多股票回测
multi_stock_performance = multi_stock_portfolio_backtest()
```

### 3. 本地新闻数据验证和测试

```python
def validate_local_news_data():
    """验证本地新闻数据的完整性和质量"""

    from src.config.news_config import news_config, print_news_config_status
    from src.tools.local_news_reader import get_local_multi_source_news

    # 检查配置状态
    print("📰 检查新闻数据配置...")
    print_news_config_status()

    # 检查数据可用性
    availability = news_config.check_local_data_availability()

    print("\n📊 数据可用性详情:")
    for source, info in availability.items():
        print(f"\n{info['name']}:")
        print(f"  目录: {info['directory']}")
        print(f"  状态: {info['status']}")
        print(f"  文件数量: {info['file_count']}")
        if info['latest_date']:
            print(f"  最新数据: {info['latest_date']}")

    # 测试数据读取
    print("\n🧪 测试数据读取...")
    test_date = "2024-01-15"  # 使用一个测试日期

    for source in ["alpha_vantage", "newsapi", "finnhub"]:
        try:
            news_data = get_local_multi_source_news(
                ticker="AAPL",
                limit=3,
                sources=[source],
                date=test_date
            )
            print(f"✅ {source}: 成功读取 {len(news_data)} 条新闻")

            # 显示第一条新闻的详情
            if news_data:
                first_news = news_data[0]
                print(f"   标题: {first_news.title[:50]}...")
                print(f"   来源: {first_news.source}")
                print(f"   日期: {first_news.date}")

        except Exception as e:
            print(f"❌ {source}: 读取失败 - {e}")

    return availability

# 运行验证
data_status = validate_local_news_data()
```

### 4. 性能对比测试

```python
def compare_local_vs_api_performance():
    """对比本地数据和API数据的性能差异"""

    import time
    from src.config.news_config import news_config

    test_params = {
        "agent": run_hedge_fund,
        "tickers": ["AAPL"],
        "start_date": "2024-01-01",
        "end_date": "2024-01-05",  # 短期测试
        "initial_capital": 100000,
        "model_name": "gpt-4o",
        "selected_analysts": ["news_analyst"],
        "show_reasoning": False
    }

    results = {}

    # 测试本地数据模式
    print("🏠 测试本地数据模式...")
    news_config.set_use_local_data(True)

    start_time = time.time()
    backtester_local = Backtester(**test_params)
    performance_local = backtester_local.run_backtest()
    local_time = time.time() - start_time

    results["local"] = {
        "time": local_time,
        "performance": performance_local
    }

    # 测试API模式（如果可用）
    print("\n🌐 测试API模式...")
    news_config.set_use_local_data(False)

    try:
        start_time = time.time()
        backtester_api = Backtester(**test_params)
        performance_api = backtester_api.run_backtest()
        api_time = time.time() - start_time

        results["api"] = {
            "time": api_time,
            "performance": performance_api
        }
    except Exception as e:
        print(f"⚠️ API模式测试失败: {e}")
        results["api"] = None

    # 对比结果
    print(f"\n📊 性能对比结果:")
    print(f"本地数据模式耗时: {local_time:.2f} 秒")

    if results["api"]:
        print(f"API模式耗时: {results['api']['time']:.2f} 秒")
        speedup = results["api"]["time"] / local_time
        print(f"本地数据加速比: {speedup:.2f}x")

    # 恢复本地数据模式
    news_config.set_use_local_data(True)

    return results

# 运行性能对比
performance_comparison = compare_local_vs_api_performance()
```

### 5. 批量回测和参数优化

```python
def batch_backtest_parameter_optimization():
    """批量回测进行参数优化"""

    import itertools
    import pandas as pd

    # 定义参数网格
    param_grid = {
        "initial_capital": [50000, 100000, 200000],
        "margin_requirement": [0.0, 0.3, 0.5],
        "analysts": [
            ["technical_analyst"],
            ["fundamental_analyst"],
            ["technical_analyst", "fundamental_analyst"],
            ["technical_analyst", "fundamental_analyst", "news_analyst"]
        ]
    }

    # 生成参数组合
    param_combinations = list(itertools.product(
        param_grid["initial_capital"],
        param_grid["margin_requirement"],
        param_grid["analysts"]
    ))

    results = []

    print(f"🔄 开始批量回测，共 {len(param_combinations)} 种参数组合...")

    for i, (capital, margin, analysts) in enumerate(param_combinations, 1):
        print(f"\n📊 测试组合 {i}/{len(param_combinations)}")
        print(f"   初始资金: ${capital:,}")
        print(f"   保证金要求: {margin*100:.0f}%")
        print(f"   分析师: {', '.join(analysts)}")

        try:
            backtester = Backtester(
                agent=run_hedge_fund,
                tickers=["AAPL"],
                start_date="2024-01-01",
                end_date="2024-01-15",  # 短期测试
                initial_capital=capital,
                selected_analysts=analysts,
                initial_margin_requirement=margin,
                show_reasoning=False
            )

            performance_metrics = backtester.run_backtest()

            # 记录结果
            result = {
                "initial_capital": capital,
                "margin_requirement": margin,
                "analysts": ", ".join(analysts),
                "sharpe_ratio": performance_metrics.get("sharpe_ratio", 0),
                "max_drawdown": performance_metrics.get("max_drawdown", 0),
                "final_value": backtester.portfolio_values[-1]["Portfolio Value"] if backtester.portfolio_values else capital
            }

            result["total_return"] = ((result["final_value"] - capital) / capital) * 100
            results.append(result)

            print(f"   ✅ 完成 - 收益率: {result['total_return']:.2f}%")

        except Exception as e:
            print(f"   ❌ 失败: {e}")
            continue

    # 分析结果
    results_df = pd.DataFrame(results)

    if not results_df.empty:
        print(f"\n📈 最佳参数组合 (按夏普比率):")
        best_sharpe = results_df.loc[results_df["sharpe_ratio"].idxmax()]
        print(f"   初始资金: ${best_sharpe['initial_capital']:,}")
        print(f"   保证金要求: {best_sharpe['margin_requirement']*100:.0f}%")
        print(f"   分析师: {best_sharpe['analysts']}")
        print(f"   夏普比率: {best_sharpe['sharpe_ratio']:.2f}")
        print(f"   总收益率: {best_sharpe['total_return']:.2f}%")

        # 保存结果
        results_df.to_csv("backtest_optimization_results.csv", index=False)
        print(f"\n💾 结果已保存到 backtest_optimization_results.csv")

    return results_df

# 运行参数优化
optimization_results = batch_backtest_parameter_optimization()
```

## 最佳实践

### 1. 数据管理
- 定期更新本地新闻数据
- 验证数据完整性和格式
- 备份重要的历史数据

### 2. 性能优化
- 使用本地数据模式提高速度
- 合理设置回测时间范围
- 选择适当的LLM模型

### 3. 结果分析
- 保存推理日志用于调试
- 跟踪准确性指标
- 进行参数敏感性分析

### 4. 风险管理
- 设置合理的保证金要求
- 监控最大回撤
- 分散投资组合

## 扩展功能

系统支持以下扩展功能：

1. **自定义分析师**：可以添加新的分析师类型
2. **多种LLM模型**：支持OpenAI、Anthropic、Ollama等
3. **实时数据流**：可以扩展为实时交易系统
4. **风险指标**：可以添加更多风险管理指标
5. **可视化增强**：可以添加更丰富的图表和报告

## 本地新闻数据支持分析

### ✅ 当前支持状态

经过详细代码分析，**`src/backtester.py` 已经完全支持本地新闻数据功能**。具体实现如下：

#### 1. 配置层面支持
- **新闻配置模块** (`src/config/news_config.py`)：提供完整的配置管理
- **命令行参数支持**：backtester.py 在第886-898行集成了新闻配置参数
- **环境变量支持**：可通过环境变量控制新闻数据源

#### 2. 数据获取层面支持
- **本地数据读取器** (`src/tools/local_news_reader.py`)：支持三个数据源的本地文件读取
- **统一API接口** (`src/tools/api.py`)：`get_formatted_multi_source_news()` 函数自动检测并使用本地数据
- **智能降级机制**：本地数据不可用时自动回退到API模式

#### 3. 代理层面支持
- **新闻分析师** (`src/agents/factual_news_agent.py`, `src/agents/subjective_news_agent.py`)：
  - 第61-73行：自动检测配置的新闻源和数据模式
  - 第67行：调用 `get_formatted_multi_source_news()` 获取新闻数据
  - 第84-96行：提供API模式降级支持

#### 4. 回测流程集成
- **数据预取** (`src/backtester.py` 第438行)：预取新闻数据到缓存
- **代理调用** (第506-515行)：通过 `run_hedge_fund` 函数调用各个分析师
- **新闻数据流**：新闻数据通过分析师传递给投资组合管理器

### 🔧 如何配置和使用本地新闻数据

#### 方法1：命令行参数
```bash
python src/backtester.py \
    --tickers AAPL \
    --start-date 2024-01-01 \
    --end-date 2024-01-31 \
    --initial-capital 100000 \
    --use-local-news \
    --news-sources alpha_vantage,newsapi,finnhub \
    --news-time-offset 1
```

#### 方法2：环境变量
```bash
export NEWS_USE_LOCAL_DATA="true"
export NEWS_SOURCES="alpha_vantage,newsapi,finnhub"
export NEWS_TIME_OFFSET_DAYS="1"

python src/backtester.py --tickers AAPL --start-date 2024-01-01 --end-date 2024-01-31
```

#### 方法3：配置文件
```python
from src.config.news_config import news_config

# 配置本地数据模式
news_config.set_use_local_data(True)
news_config.set_selected_sources(["alpha_vantage", "newsapi", "finnhub"])
news_config.set_time_offset_days(1)
news_config.save_config()

# 运行回测
from src.backtester import Backtester
from src.main import run_hedge_fund

backtester = Backtester(
    agent=run_hedge_fund,
    tickers=["AAPL"],
    start_date="2024-01-01",
    end_date="2024-01-31",
    initial_capital=100000
)
performance_metrics = backtester.run_backtest()
```

### 📁 本地数据目录要求

确保以下目录结构存在：
```
项目根目录/
├── AAPL_alpha_news/          # Alpha Vantage 新闻数据
│   ├── alpha_news_2024-01-01.json
│   ├── alpha_news_2024-01-02.json
│   └── ...
├── AAPL_news_api_news/       # NewsAPI 新闻数据
│   ├── news_api_2024-01-01.json
│   ├── news_api_2024-01-02.json
│   └── ...
└── AAPL_finnhub_news/        # Finnhub 新闻数据
    ├── finnhub_2024-01-01.json
    ├── finnhub_2024-01-02.json
    └── ...
```

### 🔍 验证本地数据支持

```python
# 检查配置状态
from src.config.news_config import print_news_config_status
print_news_config_status()

# 检查数据可用性
from src.config.news_config import news_config
availability = news_config.check_local_data_availability()
for source, info in availability.items():
    print(f"{source}: {info['status']}")

# 测试数据读取
from src.tools.local_news_reader import get_local_multi_source_news
news_data = get_local_multi_source_news(
    ticker="AAPL",
    limit=5,
    sources=["alpha_vantage", "newsapi", "finnhub"],
    date="2024-01-15"
)
print(f"成功读取 {len(news_data)} 条新闻")
```

### ⚡ 使用本地数据的优势

1. **避免API速率限制**：不受第三方API调用频率限制
2. **提高回测速度**：本地文件读取比网络请求快10-50倍
3. **确保数据一致性**：使用固定的历史数据集，结果可重现
4. **降低运行成本**：减少API调用费用
5. **离线工作能力**：无需网络连接即可运行完整回测

### 🎯 总结

**当前的 `backtester.py` 已经完全支持本地新闻数据功能**，无需任何代码修改。系统提供了：

- ✅ 完整的配置管理系统
- ✅ 三个数据源的本地数据支持 (Alpha Vantage, NewsAPI, Finnhub)
- ✅ 智能的API/本地数据切换机制
- ✅ 命令行参数和环境变量支持
- ✅ 错误处理和降级机制
- ✅ 与现有回测流程的无缝集成

用户只需要：
1. 准备好本地新闻数据文件
2. 配置相应的参数或环境变量
3. 正常运行回测即可享受本地数据的所有优势
