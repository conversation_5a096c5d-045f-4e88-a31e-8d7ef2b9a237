import json
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate

from src.graph.state import AgentState, show_agent_reasoning
from pydantic import BaseModel, Field
from typing_extensions import Literal
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.agents.reflection_analyst import load_previous_reflection


class PortfolioDecision(BaseModel):
    action: Literal["buy", "sell", "short", "cover", "hold"]
    quantity: int = Field(description="Number of shares to trade")
    confidence: float = Field(description="Confidence in the decision, between 0.0 and 100.0")
    reasoning: str = Field(description="Reasoning for the decision")


class PortfolioManagerOutput(BaseModel):
    decisions: dict[str, PortfolioDecision] = Field(description="Dictionary of ticker to trading decisions")


##### Portfolio Management Agent #####
def portfolio_management_agent(state: AgentState):
    """Makes final trading decisions and generates orders for multiple tickers"""

    # Get the portfolio and analyst signals
    portfolio = state["data"]["portfolio"]
    analyst_signals = state["data"]["analyst_signals"]
    tickers = state["data"]["tickers"]
    end_date = state["data"]["end_date"]

    # 加载前一日的反思建议（如果存在）
    previous_reflections = load_previous_reflection(end_date, tickers)
    if previous_reflections:
        progress.update_status("portfolio_manager", None, "Loaded previous day reflections")
    else:
        progress.update_status("portfolio_manager", None, "No previous reflections found (first day or no data)")

    # Get position limits, current prices, and signals for every ticker
    position_limits = {}
    current_prices = {}
    max_shares = {}
    signals_by_ticker = {}
    for ticker in tickers:
        progress.update_status("portfolio_manager", ticker, "Processing analyst signals")

        # Get position limits and current prices for the ticker
        risk_data = analyst_signals.get("risk_management_agent", {}).get(ticker, {})
        position_limits[ticker] = risk_data.get("remaining_position_limit", 0)
        current_prices[ticker] = risk_data.get("current_price", 0)

        # Calculate maximum shares allowed based on position limit and price
        if current_prices[ticker] > 0:
            max_shares[ticker] = int(position_limits[ticker] / current_prices[ticker])
        else:
            max_shares[ticker] = 0

        # Get signals for the ticker
        ticker_signals = {}
        for agent, signals in analyst_signals.items():
            if agent != "risk_management_agent" and ticker in signals:
                # Extract basic signal and confidence
                signal_data = {
                    "signal": signals[ticker]["signal"],
                    "confidence": signals[ticker]["confidence"]
                }

                # Extract reasoning if available and convert to string format
                if "reasoning" in signals[ticker] and signals[ticker]["reasoning"] is not None:
                    reasoning = signals[ticker]["reasoning"]
                    # Handle both string and dict formats, convert to string
                    if isinstance(reasoning, dict):
                        signal_data["reasoning"] = str(reasoning)
                    else:
                        signal_data["reasoning"] = str(reasoning)
                else:
                    # No reasoning available for this agent
                    signal_data["reasoning"] = None

                ticker_signals[agent] = signal_data
        signals_by_ticker[ticker] = ticker_signals

    progress.update_status("portfolio_manager", None, "Generating trading decisions")

    # Generate the trading decision
    result = generate_trading_decision(
        tickers=tickers,
        signals_by_ticker=signals_by_ticker,
        current_prices=current_prices,
        max_shares=max_shares,
        portfolio=portfolio,
        previous_reflections=previous_reflections,
        model_name=state["metadata"]["model_name"],
        model_provider=state["metadata"]["model_provider"],
    )

    # Create the portfolio management message
    message = HumanMessage(
        content=json.dumps({ticker: decision.model_dump() for ticker, decision in result.decisions.items()}),
        name="portfolio_manager",
    )

    # Print the decision if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning({ticker: decision.model_dump() for ticker, decision in result.decisions.items()}, "Portfolio Manager")

    progress.update_status("portfolio_manager", None, "Done")

    return {
        "messages": state["messages"] + [message],
        "data": state["data"],
    }


def generate_trading_decision(
    tickers: list[str],
    signals_by_ticker: dict[str, dict],
    current_prices: dict[str, float],
    max_shares: dict[str, int],
    portfolio: dict[str, float],
    previous_reflections: dict[str, dict],
    model_name: str,
    model_provider: str,
) -> PortfolioManagerOutput:
    """Attempts to get a decision from the LLM with retry logic"""
    # Create the prompt template
    template = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """You are a portfolio manager making final trading decisions based on multiple tickers.

              Trading Rules:
              - For long positions:
                * Only buy if you have available cash
                * Only sell if you currently hold long shares of that ticker
                * Sell quantity must be ≤ current long position shares
                * Buy quantity must be ≤ max_shares for that ticker

              - For short positions:
                * Only short if you have available margin (position value × margin requirement)
                * Only cover if you currently have short shares of that ticker
                * Cover quantity must be ≤ current short position shares
                * Short quantity must respect margin requirements

              - The max_shares values are pre-calculated to respect position limits
              - Consider both long and short opportunities based on signals
              - Maintain appropriate risk management with both long and short exposure

              Available Actions:
              - "buy": Open or add to long position
              - "sell": Close or reduce long position
              - "short": Open or add to short position
              - "cover": Close or reduce short position
              - "hold": No action

              Reflection Learning:
              - If previous day reflections are provided, carefully consider the insights and recommendations
              - Learn from past decision quality assessments to improve current decisions
              - Apply the specific recommendations from the reflection analyst when relevant

              Signal Analysis and Reasoning Integration:
              - Each analyst signal may include detailed reasoning explaining their investment thesis

              Value Investing Perspectives:
              - Warren Buffett agent provides value investing reasoning with margin of safety analysis
              - Ben Graham agent provides defensive value investing reasoning with net-net and asset-based analysis
              - Charlie Munger agent provides rational thinking reasoning with moat analysis and mental models

              Growth and Innovation Perspectives:
              - Phil Fisher agent provides growth investing reasoning with management quality emphasis
              - Cathie Wood agent provides disruptive innovation reasoning with exponential growth potential
              - Peter Lynch agent provides practical growth reasoning with PEG ratios and ten-bagger potential

              Activist and Contrarian Perspectives:
              - Bill Ackman agent provides activist investing reasoning with brand and operational improvement focus
              - Michael Burry agent provides contrarian reasoning with market inefficiency and deep value analysis

              Macro and Technical Perspectives:
              - Stanley Druckenmiller agent provides macro and momentum reasoning with risk-reward analysis
              - Aswath Damodaran agent provides academic valuation reasoning with DCF and relative valuation

              Fundamental and News Analysis:
              - Fundamentals agent provides quantitative fundamental analysis reasoning
              - Sentiment agent provides market sentiment reasoning based on insider trading and news
              - Factual News agent provides objective news analysis reasoning
              - Subjective News agent provides sentiment-driven news analysis reasoning

              Quantitative Signals (No Reasoning):
              - Technical analyst provides quantitative signals based on price action and market indicators
              - Valuation agent provides pure valuation metrics without detailed reasoning

              Decision Framework:
              - Synthesize these different perspectives and reasoning approaches to make informed decisions
              - Weight the reasoning quality and conviction level when making final decisions
              - If an agent provides strong reasoning that aligns with market conditions, give it higher consideration
              - Consider the complementary nature of different investment philosophies

              Inputs:
              - signals_by_ticker: dictionary of ticker → signals (including reasoning where available)
              - max_shares: maximum shares allowed per ticker
              - portfolio_cash: current cash in portfolio
              - portfolio_positions: current positions (both long and short)
              - current_prices: current prices for each ticker
              - margin_requirement: current margin requirement for short positions (e.g., 0.5 means 50%)
              - total_margin_used: total margin currently in use
              - previous_reflections: insights and recommendations from previous day's decision analysis (if available)
              """,
            ),
            (
                "human",
                """Based on the comprehensive team analysis, make your trading decisions for each ticker.

              Here are the signals by ticker (including detailed reasoning where available):
              {signals_by_ticker}

              IMPORTANT: Pay special attention to the reasoning provided by each analyst:

              Value Investing Reasoning:
              - Warren Buffett's reasoning focuses on intrinsic value, margin of safety, and long-term fundamentals
              - Ben Graham's reasoning emphasizes defensive value investing with asset protection and earnings stability
              - Charlie Munger's reasoning highlights rational thinking, moat analysis, and mental models

              Growth and Innovation Reasoning:
              - Phil Fisher's reasoning highlights growth quality, management efficiency, and innovation potential
              - Cathie Wood's reasoning focuses on disruptive innovation, exponential growth, and transformative technologies
              - Peter Lynch's reasoning emphasizes practical growth investing, PEG ratios, and understandable businesses

              Activist and Contrarian Reasoning:
              - Bill Ackman's reasoning emphasizes brand strength, activism potential, and operational improvements
              - Michael Burry's reasoning focuses on contrarian opportunities, market inefficiencies, and deep value

              Macro and Academic Reasoning:
              - Stanley Druckenmiller's reasoning covers macro trends, momentum, and asymmetric risk-reward
              - Aswath Damodaran's reasoning provides academic valuation analysis with DCF models and relative metrics

              Fundamental and News Analysis Reasoning:
              - Fundamentals agent reasoning covers quantitative financial metrics and ratios analysis
              - Sentiment agent reasoning analyzes market sentiment through insider trading and news patterns
              - Factual News agent reasoning provides objective analysis of company-specific news events
              - Subjective News agent reasoning interprets market sentiment and opinion-based news analysis

              Quantitative Signals (Limited Reasoning):
              - Technical analyst provides quantitative signals based on price action and market indicators
              - Valuation agent provides pure valuation metrics without detailed reasoning

              Synthesize these diverse investment philosophies and reasoning approaches to make well-informed decisions.
              Consider how different perspectives complement or contradict each other to form a comprehensive view.

              Current Prices:
              {current_prices}

              Maximum Shares Allowed For Purchases:
              {max_shares}

              Portfolio Cash: {portfolio_cash}
              Current Positions: {portfolio_positions}
              Current Margin Requirement: {margin_requirement}
              Total Margin Used: {total_margin_used}

              Previous Day Reflections (learn from these insights):
              {previous_reflections}

              Output strictly in JSON with the following structure:
              {{
                "decisions": {{
                  "TICKER1": {{
                    "action": "buy/sell/short/cover/hold",
                    "quantity": integer,
                    "confidence": float between 0 and 100,
                    "reasoning": "string"
                  }},
                  "TICKER2": {{
                    ...
                  }},
                  ...
                }}
              }}
              """,
            ),
        ]
    )

    # Generate the prompt
    prompt = template.invoke(
        {
            "signals_by_ticker": json.dumps(signals_by_ticker, indent=2),
            "current_prices": json.dumps(current_prices, indent=2),
            "max_shares": json.dumps(max_shares, indent=2),
            "portfolio_cash": f"{portfolio.get('cash', 0):.2f}",
            "portfolio_positions": json.dumps(portfolio.get("positions", {}), indent=2),
            "margin_requirement": f"{portfolio.get('margin_requirement', 0):.2f}",
            "total_margin_used": f"{portfolio.get('margin_used', 0):.2f}",
            "previous_reflections": json.dumps(previous_reflections, indent=2, ensure_ascii=False) if previous_reflections else "无前一日反思数据（首日交易或数据不可用）",
        }
    )

    # Create default factory for PortfolioManagerOutput
    def create_default_portfolio_output():
        return PortfolioManagerOutput(decisions={ticker: PortfolioDecision(action="hold", quantity=0, confidence=0.0, reasoning="Error in portfolio management, defaulting to hold") for ticker in tickers})

    return call_llm(prompt=prompt, model_name=model_name, model_provider=model_provider, pydantic_model=PortfolioManagerOutput, agent_name="portfolio_manager", default_factory=create_default_portfolio_output)
